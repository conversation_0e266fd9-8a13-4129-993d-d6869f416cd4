<?php

// --- bootstrap --------------------------------------------------------
$routes = [
    '/home' => 'home/',
    '/movies' => 'movies/',
    '/movies/1' => 'single/',
    '/subscriptions' => 'subscriptions/',
    '/contact-us' => 'contact-us/',
    '/login' => 'login/',
];

// strip query-string and trailing slash (except for root)
$uri = strtok($_SERVER['REQUEST_URI'], '?');
$uri = rtrim($uri, '/') ?: '/';

// --- routing ----------------------------------------------------------
if (!array_key_exists($uri, $routes)) {
    http_response_code(404);
    $view = 'pages/404.php';              // create this file if you like
} else {
    $view = $routes[$uri];
}

$viewPath = PUBLIC_FOLDER . "pages/" . $view . "index.php";

