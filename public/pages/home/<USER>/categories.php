<?php
$categories = [
    ['name' => 'Action', 'link' => '/category/action'],
    ['name' => 'Adventure', 'link' => '/category/adventure'],
    ['name' => 'Comedy', 'link' => '/category/comedy'],
    ['name' => 'Drama', 'link' => '/category/drama'],
    ['name' => 'Horror', 'link' => '/category/horror'],
];

// Image list
$images = ['movie1.png', 'movie2.png', 'movie3.png', 'movie2.png'];
$imageCount = count($images);
?>

<section id="categories" class="tb-sm:hidden w-full max-w-5xl flex flex-col justify-center items-start gap-10 px-4">
    <?php Title("Explore our wide variety of categories") ?>
    <?php Paragraph("Whether you're looking for a comedy to make you laugh, a drama to make you think, or a documentary to learn something new") ?>

    <div class="self-stretch flex flex-col justify-center items-start gap-6">
        <div class="swiper categories-container w-full">
            <div class="swiper-wrapper flex gap-4 py-8">

                <?php foreach ($categories as $index => $cat): ?>
                    <a href="<?= htmlspecialchars($cat['link']) ?>"
                        class="swiper-slide group flex-shrink-0 w-64 group hover:scale-105">
                        <div
                            class="flex-1 p-5 bg-zinc-900 rounded-[10px] outline outline-1 outline-offset-[-1px] outline-neutral-800 flex flex-col justify-start items-start transition-all duration-300 ease-in-out transform group-hover:bg-zinc-800">
                            <div class="relative self-stretch h-36 grid grid-cols-2 grid-rows-2 gap-[5px]">
                                <?php for ($i = 0; $i < 4; $i++): ?>
                                    <img class="w-full h-full rounded-md object-cover object-center"
                                        src="<?= IMAGE_SRC . "temp/" . $images[$i] ?>" />
                                <?php endfor; ?>
                                <div class="absolute inset-0 bg-gradient-to-b from-zinc-900/0 to-90% to-zinc-900">
                                </div>
                            </div>
                            <div class="self-stretch flex justify-start items-center">
                                <div class="flex-1 text-white text-sm font-semibold font-['Manrope'] leading-tight">
                                    <?= htmlspecialchars($cat['name']) ?>
                                </div>
                                <?= icon("material-symbols:arrow-forward-rounded", "text-white text-xl") ?>
                            </div>
                        </div>
                    </a>
                <?php endforeach; ?>

            </div>

            <!-- mobile Pagination -->
            <div class="mb-pagination mb-xl:hidden mt-6 flex justify-center items-start gap-[3px]">
                <?php foreach ($categories as $index => $category) { ?>
                    <div class="progress-item w-6 h-1 bg-zinc-800 rounded-[100px] cursor-pointer"></div>
                <?php } ?>
            </div>
        </div>
    </div>
</section>