<div class="w-full pt-28 pb-14 flex flex-col justify-start items-center gap-20">
    <div class="self-stretch flex flex-col justify-start items-center gap-5">
        <div
            class="text-center justify-start text-white mb-md:text-4xl text-2xl font-bold font-['Manrope'] leading-9 [text-shadow:_0px_25px_50px_rgb(0_0_0_/_0.25)]">
            Welcome Back to NetflixCheap!</div>
        <div
            class="max-w-[800px] text-center justify-start text-neutral-400 mb-md:text-lg text-sm font-normal font-['Manrope'] leading-relaxed px-4">
            Sign in to manage your subscriptions and keep streaming.</div>
    </div>
    <div class="self-stretch flex justify-center items-start gap-10 px-4">
        <form action="/login" method="POST"
            class="w-full max-w-[700px] mb-md:p-12 p-6 bg-stone-950 rounded-xl outline outline-1 outline-offset-[-1px] outline-neutral-800 flex flex-col justify-start items-center mb-md:gap-12 gap-8">
            <div class="flex flex-col justify-start items-center gap-7 w-full">
                <div class="flex justify-center items-center gap-[3px]">
                    <div class="flex justify-start items-center gap-1.5 overflow-hidden">
                        <div class="w-11 h-12 bg-red-600"></div>
                        <div class="w-4 h-4 bg-white"></div>
                        <div class="w-5 h-6 bg-orange-50"></div>
                        <div class="justify-start"><span
                                class="text-white mb-md:text-4xl text-2xl font-extrabold font-['Open_Sans'] leading-[54px]">Netflix</span><span
                                class="text-white mb-md:text-4xl text-2xl font-semibold font-['Open_Sans'] leading-[54px]">Cheap</span>
                        </div>
                    </div>
                </div>

                <!-- Email Field -->
                <div class="w-full max-w-[600px]">
                    <?php renderInputField("Email Address", "<EMAIL>", "email", "email", "email") ?>
                </div>

                <!-- Password Field -->
                <div class="w-full max-w-[600px]">
                    <?php renderPasswordField("Password", "Enter your password", "password", "password") ?>
                </div>

                <!-- Sign In Button -->
                <div class="w-full max-w-[600px]">
                    <?php renderSubmitButton("Sign In") ?>
                </div>

                <div
                    class="text-center justify-start text-neutral-400 text-base font-normal font-['Manrope'] leading-normal">
                    <a href="/forgot-password" class="hover:text-white transition-colors">Forgot your password?</a>
                </div>
            </div>

            <div class="flex flex-col justify-start items-center gap-7 w-full">
                <div
                    class="text-center justify-start text-white mb-md:text-xl text-lg font-semibold font-['Manrope'] leading-loose">
                    New to NetflixCheap?</div>
                <div class="w-full max-w-[600px]">
                    <?php renderSecondaryButton("Create an account", "/signup") ?>
                </div>
            </div>
        </form>
    </div>
</div>