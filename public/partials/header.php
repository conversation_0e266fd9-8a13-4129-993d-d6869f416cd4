<?php

include_once __DIR__ . '/elements.php';
include_once __DIR__ . '/components/product.php';
include_once __DIR__ . '/components/filter.php';
include_once __DIR__ . '/components/form/input-field.php';
include_once __DIR__ . '/components/form/textarea.php';
include_once __DIR__ . '/components/form/checkbox.php';
include_once __DIR__ . '/components/form/submit-button.php';
include_once __DIR__ . '/components/form/secondary-button.php';
include_once __DIR__ . '/components/form/password-field.php';
include_once __DIR__ . '/components/form/country-code-dropdown.php';


// Get current URL path
$currentPath = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

$menuItems = [
    ['text' => 'Home', 'link' => '/home'],
    ['text' => 'Movies & Shows', 'link' => '/movies'],
    ['text' => 'Subscriptions', 'link' => '/subscriptions'],
    ['text' => 'Contact Us', 'link' => '/contact-us']
];
?>

<header
    class="sticky top-0 z-20 w-full tb-sm:py-5 backdrop-blur-sm overflow-hidden transition-all duration-300 ease-in-out">
    <?php
    include_once __DIR__ . '/mobile/header.php';
    ?>
    <div
        class="max-tb-sm:hidden p-[1px] lp-sm:mx-20 tb-lg:mx-20 mx-10 bg-gradient-to-r from-[#939393] to-[#3F3D3D] rounded-2xl backdrop-blur-sm overflow-hidden transition-all duration-300 ease-in-out">
        <div class="flex justify-between items-center w-full bg-[#09090B] rounded-2xl p-5">
            <div class="flex justify-center items-center gap-[3px]">
                <a href="/" class="flex justify-start items-center gap-1.5 overflow-hidden">
                    <div>
                        <img src="<?= IMAGE_SRC ?>logo.svg"></img>
                    </div>
                    <div>
                        <span class="text-2xl font-bold text-white">NetflexCheap</span>
                    </div>
                </a>
            </div>
            <div
                class="px-10 py-2.5rounded-xl shadow-[0px_4px_14px_10px_rgba(0,0,0,0.11)] flex justify-start items-center gap-7 overflow-hidden">
                <?php
                foreach ($menuItems as $item) {
                    $isSelected = $currentPath === $item['link'];
                    if ($isSelected) {
                        ?>
                        <a href="<?= $item['link'] ?>"
                            class="px-6 py-3.5 bg-[#2C2C2C] rounded-lg flex justify-start items-center gap-2.5">
                            <span
                                class="justify-start text-white mb-md:text-lg text-sm font-medium font-[\'Manrope\'] leading-none transition-all duration-300 animate-pulse">
                                <?= $item['text'] ?>
                            </span>
                        </a>
                        <?php
                    } else {
                        ?>
                        <a href="<?= $item['link'] ?>"
                            class="px-6 py-3.5 flex justify-start text-zinc-300 mb-md:text-lg text-sm font-normal font-[\'Manrope\'] leading-none hover:text-white transition-all duration-300 hover:scale-110">
                            <?= $item['text'] ?>
                        </a>
                        <?php
                    }
                }
                ?>
            </div>
            <div class="backdrop-blur-md flex justify-start items-center gap-5">
                <a href="/signup"
                    class="h-14 px-2.5 rounded-lg flex justify-center items-center gap-2.5 overflow-hidden hover:scale-110 transition-all duration-300">
                    <span
                        class="justify-center text-zinc-300 mb-md:text-lg text-sm font-medium font-['Inter'] hover:text-white transition-all duration-300">
                        Sign up
                    </span>
                </a>
                <a href="/login"
                    class="flex justify-center items-center p-0.5 bg-gradient-to-r from-[#939393] to-[#3F3D3D] backdrop-blur rounded-[88px] hover:scale-110 transition-all duration-300">
                    <div
                        class="px-7 py-3.5 bg-[#09090B] rounded-[88px] flex justify-center items-center gap-2.5 overflow-hidden">
                        <?php icon('material-symbols:account-circle-full', class: 'text-zinc-300 text-xl'); ?>
                        <span
                            class="justify-center text-zinc-300 mb-md:text-lg text-sm font-medium font-['Inter'] group-hover:text-white transition-all duration-300">
                            Login
                        </span>
                    </div>
                </a>
            </div>
        </div>
</header>