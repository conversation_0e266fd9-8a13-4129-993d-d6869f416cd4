<?php
function renderTextArea(string $label, string $placeholder, string $name = "", string $id = "", bool $required = true): void
{
    ?>
    <div class="self-stretch flex flex-col justify-start items-start gap-4">
        <?php if (!empty($label)): ?>
            <label for="<?= htmlspecialchars($id) ?>"
                class="text-white mb-md:text-lg text-base font-semibold leading-relaxed font-manrope">
                <?= htmlspecialchars($label) ?>
            </label>
        <?php endif; ?>
        <div
            class="self-stretch mb-md:h-40 h-32 mb-md:p-5 p-4 bg-neutral-900 rounded-lg outline outline-1 outline-offset-[-1px] outline-neutral-800 flex justify-start items-start gap-20">
            <textarea id="<?= htmlspecialchars($id) ?>" name="<?= htmlspecialchars($name) ?>"
                placeholder="<?= htmlspecialchars($placeholder) ?>"
                class="flex-1 resize-none text-neutral-400 mb-md:text-lg text-sm font-normal leading-relaxed font-manrope bg-transparent border-none outline-none"
                <?= $required ? 'required' : '' ?>></textarea>
        </div>
    </div>
    <?php
}