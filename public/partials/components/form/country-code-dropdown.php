<?php

function renderCountryCode(): void
{
    $jsonPath = ROOT_FOLDER . 'json/countries.json';

    if (!file_exists($jsonPath)) {
        echo "<div class='text-red-500'>countries.json not found at: $jsonPath</div>";
        return;
    }

    $jsonData = file_get_contents($jsonPath);
    $countries = json_decode($jsonData, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        echo "<div class='text-red-500'>Invalid JSON in countries.json</div>";
        return;
    }

    // Find Morocco as the default country
    $defaultCountry = $countries[0]; // Fallback to first country
    foreach ($countries as $country) {
        if ($country['countryCallingCode'] === '+212' && $country['name'] === 'Morocco') {
            $defaultCountry = $country;
            break;
        }
    }
    ?>
    <div class="relative inline-block text-left">
        <button id="country-code-button" type="button"
            class="flex items-center h-full gap-2 px-3 py-2 bg-neutral-900 rounded-l-lg outline outline-1 outline-offset-[-1px] outline-neutral-800 hover:bg-neutral-800 focus:outline-none transition duration-150">
            <img id="flag-img" src="<?= htmlspecialchars($defaultCountry['flags']['png']) ?>"
                alt="<?= htmlspecialchars($defaultCountry['name']) ?>"
                class="mb-md:w-11 w-9 h-auto object-cover rounded-md overflow-hidden">
            <?= icon("ic:outline-keyboard-arrow-down", "text-neutral-400 mb-md:text-2xl text-xl") ?>
            <input type="hidden" id="calling-code" name="calling_code"
                value="<?= htmlspecialchars($defaultCountry['countryCallingCode']) ?>" required>
        </button>

        <div id="country-code-menu"
            class="hidden absolute z-10 mt-1 w-56 max-h-60 overflow-auto rounded-md bg-neutral-900 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
            <div class="py-1">
                <?php foreach ($countries as $country): ?>
                    <div data-code="<?= htmlspecialchars($country['countryCallingCode']) ?>"
                        data-flag="<?= htmlspecialchars($country['flags']['png']) ?>"
                        class="flex items-center gap-2 px-4 py-2 text-sm text-white cursor-pointer hover:bg-neutral-800">
                        <img src="<?= htmlspecialchars($country['flags']['png']) ?>"
                            alt="<?= htmlspecialchars($country['name']) ?>" class="w-5 h-3 object-cover rounded-sm">
                        <span><?= htmlspecialchars("{$country['countryCallingCode']} - {$country['name']}") ?></span>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    <?php
}
?>