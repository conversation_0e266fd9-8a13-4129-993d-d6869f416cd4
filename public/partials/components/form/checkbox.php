<?php
function renderCheckbox(string $label, string $name = "", bool $checked = false, string $value = ""): void
{
    $uniqueId = !empty($name) ? htmlspecialchars($name) : 'checkbox_' . uniqid();
    $checkedAttr = $checked ? 'checked' : '';
    $dataSelected = $checked ? 'true' : 'false';
    ?>
    <label
        class="group relative max-w-fit inline-flex items-center justify-start cursor-pointer tap-highlight-transparent p-2 -m-2 select-none checkbox-wrapper"
        data-selected="<?= $dataSelected ?>">
        <input aria-label="<?= htmlspecialchars($label) ?>" aria-labelledby="<?= $uniqueId ?>"
            data-react-aria-pressable="true" tabindex="0"
            class="font-inherit text-[100%] leading-[1.15] m-0 p-0 overflow-visible box-border absolute top-0 w-full h-full opacity-[0.0001] z-[1] cursor-pointer disabled:cursor-[#0F0F0F] checkbox-input"
            type="checkbox" name="<?= htmlspecialchars($name) ?>" value="<?= htmlspecialchars($value) ?>" <?= $checkedAttr ?>>
        <span aria-hidden="true"
            class="relative border border-[#262626] inline-flex items-center justify-center flex-shrink-0 overflow-hidden before:content-[''] before:absolute before:inset-0 before:border-solid before:border-2 before:box-border before:border-[#0F0F0F] after:content-[''] after:absolute after:inset-0 after:scale-50 after:opacity-0 after:origin-center group-data-[selected=true]:after:scale-100 group-data-[selected=true]:after:opacity-100 group-data-[hover=true]:before:bg-[#0F0F0F] outline-none group-data-[focus-visible=true]:z-10 group-data-[focus-visible=true]:ring-2 group-data-[focus-visible=true]:ring-white group-data-[focus-visible=true]:ring-offset-2 group-data-[focus-visible=true]:ring-offset-white after:bg-red-500 after:text-white text-white w-5 h-5 me-2 rounded-md before:rounded-md after:rounded-md before:transition-colors group-data-[pressed=true]:scale-95 transition-transform after:transition-transform-opacity after:!ease-linear after:!duration-200 motion-reduce:transition-none">
            <svg aria-hidden="true" fill="none" role="presentation" stroke="currentColor" stroke-dasharray="22"
                stroke-dashoffset="44" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 17 18"
                class="z-10 opacity-0 group-data-[selected=true]:opacity-100 pointer-events-none w-4 h-3 transition-opacity motion-reduce:transition-none"
                style="transition: stroke-dashoffset 250ms linear 0.2s;">
                <polyline points="1 9 7 14 15 4"></polyline>
            </svg>
        </span>
        <?php if (!empty($label)): ?>
            <span id="<?= $uniqueId ?>"
                class="relative text-[#999999] select-none text-base transition-all before:transition-all motion-reduce:transition-none">
                <?= htmlspecialchars($label) ?>
            </span>
        <?php endif; ?>
    </label>
    <?php
}