document.addEventListener('DOMContentLoaded', function () {
    const button = document.getElementById('country-code-button');
    const menu = document.getElementById('country-code-menu');
    const flagImg = document.getElementById('flag-img');
    const callingCode = document.getElementById('calling-code');

    if (!button || !menu) return;

    button.addEventListener('click', function () {
        menu.classList.toggle('hidden');
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function (e: MouseEvent) {
        const target = e.target as HTMLElement;
        if (!button.contains(target) && !menu?.contains(target)) {
            menu?.classList.add('hidden');
        }
    });

    // Handle option click
    menu.querySelectorAll<HTMLElement>('[data-code]').forEach(option => {
        option.addEventListener('click', function () {
            const code = this.getAttribute('data-code');
            const flag = this.getAttribute('data-flag');

            if (callingCode && flagImg && code && flag) {
                // Update the hidden input value
                (callingCode as HTMLInputElement).value = code;
                // Update the flag image
                flagImg.setAttribute('src', flag);
                flagImg.setAttribute('alt', `Flag for ${code}`);
            }

            menu?.classList.add('hidden');
        });
    });
});