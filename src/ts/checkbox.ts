document.addEventListener("DOMContentLoaded", () => {
    const checkboxWrappers = document.querySelectorAll<HTMLLabelElement>(".checkbox-wrapper");

    checkboxWrappers.forEach(wrapper => {
        const input = wrapper.querySelector<HTMLInputElement>(".checkbox-input");
        
        if (!input) return;

        // Set initial state based on input's checked property
        updateCheckboxState(wrapper, input.checked);

        // Handle checkbox change events
        input.addEventListener("change", () => {
            updateCheckboxState(wrapper, input.checked);
        });

        // Handle click on the wrapper (for better UX)
        wrapper.addEventListener("click", (e) => {
            // Prevent double-triggering if the input itself was clicked
            if (e.target === input) return;
            
            // Toggle the checkbox
            input.checked = !input.checked;
            updateCheckboxState(wrapper, input.checked);
            
            // Trigger change event for form handling
            input.dispatchEvent(new Event('change', { bubbles: true }));
        });
    });

    function updateCheckboxState(wrapper: HTMLLabelElement, isChecked: boolean): void {
        wrapper.setAttribute("data-selected", isChecked.toString());
        
        // Optional: Add/remove additional classes for styling
        if (isChecked) {
            wrapper.classList.add("checked");
        } else {
            wrapper.classList.remove("checked");
        }
    }
});
