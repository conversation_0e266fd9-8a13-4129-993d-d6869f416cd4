import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

import Swiper, { Navigation, Pagination, Autoplay } from 'swiper';

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function () {
    const wrap = document.querySelector('.categories-container') as HTMLElement;
    if (wrap) {
        const bullets = Array.from(
            wrap.querySelectorAll('.progress-item')
        ) as HTMLElement[];

        const swiper = new Swiper('.categories-container', {
            modules: [Autoplay, Pagination],
            loop: true,
            slidesPerView: 2,
            spaceBetween: 16,
            centeredSlides: false,
            autoplay: {
                delay: 1500,
                disableOnInteraction: false,
            },
            pagination: {
                el: '.mb-pagination',
                bulletClass: 'progress-item',
                clickable: true,
                bulletActiveClass: 'active',
            },
            breakpoints: {
                460: {
                    slidesPerView: 1,
                },
                640: {
                    slidesPerView: 2,
                },
                768: {
                    slidesPerView: 3,
                },
                1024: {
                    slidesPerView: 4,
                },
            },
            on: {
                init: () => updateBullets(0),
                slideChange: sw => updateBullets(sw.realIndex % bullets.length),
            },
        });

        function updateBullets(idx: number) {
            bullets.forEach((b, i) => {
                b.classList.toggle('bg-red-600', i === idx);
                b.classList.toggle('bg-zinc-800', i !== idx);
                b.classList.toggle('active', i === idx);
            });
        }
    }
});