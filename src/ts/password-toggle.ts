document.addEventListener('DOMContentLoaded', function () {
    // Handle password toggle functionality
    document.querySelectorAll('.password-toggle').forEach(button => {
        button.addEventListener('click', function () {
            const targetId = this.getAttribute('data-target');
            const passwordInput = document.getElementById(targetId) as HTMLInputElement;
            const icon = this.querySelector('iconify-icon');
            
            if (passwordInput && icon) {
                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    icon.setAttribute('icon', 'mdi:eye-off-outline');
                } else {
                    passwordInput.type = 'password';
                    icon.setAttribute('icon', 'mdi:eye-outline');
                }
            }
        });
    });
});
