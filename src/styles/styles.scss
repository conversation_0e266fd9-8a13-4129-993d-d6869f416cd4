@import url('https://fonts.googleapis.com/css2?family=Kumbh+Sans:wght@100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=The+Nautigal:wght@400;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans:ital,wght@0,100..900;1,100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Manrope:wght@100..900&display=swap');


body {
  font-family: 'Inter', sans-serif;
  margin: 0;
  padding: 0;
  background-color: #09090B;
}

// movie Swiper Styles
.movie-slider {
  width: 100%;
  overflow: visible;

  .swiper-slide {
    width: auto;
    height: auto;
  }

  .swiper-pagination {
    position: relative;
    margin-top: 20px;
  }

  .swiper-pagination-bullet {
    background-color: rgba(255, 255, 255, 0.5);

    &-active {
      background-color: white;
    }
  }
}

// Swiper Custom Styles for Categories Slider
.categories-container {
  width: 100%;
  overflow: visible;

  .swiper-slide {
    width: auto;
    height: auto;
  }

  .categories-pagination {
    position: relative;
    margin-top: 20px;
    display: flex;
    justify-content: center
  }

  .swiper-pagination-bullet {
    background-color: #333333;

    &-active {
      border-radius: 8px;
      width: 20px;
      background-color: #E50000;
    }
  }
}

// mobile banner gradient
#banner-gradient {
  background-image: linear-gradient(89.97deg, #09090B 2.42%, rgba(9, 9, 11, 0.8) 25.46%, rgba(34, 14, 14, 0.909574) 46.72%, rgba(229, 0, 0, 0) 168.98%)
}

@media screen and (max-width: 768px) {

  #banner-gradient {
    background-image: linear-gradient(179.79deg, #0F0F0F 0.18%, rgba(20, 15, 15, 0.974681) 29.35%, rgba(34, 14, 14, 0.909574) 58.53%, rgba(229, 0, 0, 0) 87.7%);
  }
}

// section order in mobile
@media screen and (max-width: 768px) {
  #hero {
    order: 1;
  }

  #categories {
    order: 2;
  }

  #cards {
    order: 3;
  }

  #faq {
    order: 4;
  }

  #pricing {
    order: 5;
  }

  #testimonials {
    order: 6;
  }

  #banner {
    order: 7;
  }

  footer {
    order: 8;
  }
}

// movies page hero gradient
.movies-hero-gradient {
  background-image: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), linear-gradient(180deg, #09090B 0%, rgba(9, 9, 11, 0.7) 6.35%, rgba(9, 9, 11, 0.55) 16.24%, rgba(9, 9, 11, 0) 23.38%), linear-gradient(0deg, #09090B 0%, rgba(9, 9, 11, 0.7) 26.22%, rgba(9, 9, 11, 0.55) 39.33%, rgba(9, 9, 11, 0) 52.43%), linear-gradient(0deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3));
}

@media screen and (max-width: 640px) {
  .movies-hero-gradient {
    background-image: linear-gradient(0deg, #141414 0%, rgba(20, 20, 20, 0) 100%), url(image.png);
  }
}

// movies here slider
.hero-slider {
  width: 100%;
  height: 100%;

  .swiper-slide {
    text-align: center;
    font-size: 18px;
    background: #444;
    display: flex;
    justify-content: center;
    align-items: center;

    img {
      display: block;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}